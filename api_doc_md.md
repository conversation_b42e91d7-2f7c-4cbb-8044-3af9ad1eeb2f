# 根据手机号和活动ID查询充值订单API文档

## 修订说明

| 版本号 | 修订人 | 修订时间 | 修订内容 |
|--------|--------|----------|----------|
| V1.0.0 | 刘小伟 | 2022/09/22 | 创建文档 |

## 环境信息

- **沙箱环境地址**：https://sandbox.open.10086.cn/38857911
- **预发环境地址**：https://dev.coc.10086.cn/staging-coc
- **生产环境地址**：https://dev.coc.10086.cn/coc

以上地址以baseUrl表示。

## 功能描述

**功能描述**：根据手机号和活动id查询近三个月内的充值订单(最近50笔)

**接口方法**：POST

**接口路径**：`${baseUrl}/gateway/api/common/buy/charge/order`

**生产环境调用示例**：
```
https://dev.coc.10086.cn/coc/gateway/api/common/buy/charge/order
```

## 请求参数

| 参数名称 | 是否必须 | 参数类型 | 参数描述 |
|----------|----------|----------|----------|
| mobile | 是 | String(32) | 订购手机号 |
| activityId | 是 | Long | 活动id |

### 请求样例

```json
{
    "mobile": "13100000000",
    "activityId": 1234
}
```

## 响应结果

| 序号 | 字段名 | 是否必须 | 类型 | 说明 |
|------|--------|----------|------|------|
| 1 | code | 是 | String | 0:成功；非0：异常场景 |
| 2 | message | 是 | String | 响应消息 |
| 3 | data | 否 | List<QueryChargeOrderResp> | 订单列表，没有查到订单时为null |

### QueryChargeOrderResp 对象结构

| 序号 | 字段名 | 是否必须 | 类型 | 说明 |
|------|--------|----------|------|------|
| 3.1.0 | mobile | 是 | String | 手机号 |
| 3.1.1 | orderTime | 是 | Long | 创建时间，毫秒时间戳 |
| 3.1.2 | orderStatus | 是 | Integer | 主订单状态 |

### 订单状态说明

| 状态码 | 状态描述 |
|--------|----------|
| 100 | 初始态 |
| 200 | 待支付 |
| 201 | 支付中 |
| 202 | 支付成功 |
| 203 | 支付失败 |
| 300 | 待订购 |
| 301 | 订购中 |
| 302 | 订购成功 |
| 303 | 订购失败 |
| 304 | 订购待重试 |

### 响应示例

```json
{
    "code": "0",
    "message": "success",
    "data": [
        {
            "mobile": "13100000000",
            "orderTime": 1663833600000,
            "orderStatus": 302
        }
    ]
}
```

## 注意事项

- 查询结果限制为近三个月内的订单
- 最多返回50笔订单记录
- 当没有查询到订单时，data字段为null
- orderTime为毫秒级时间戳格式