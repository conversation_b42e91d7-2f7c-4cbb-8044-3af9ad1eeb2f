#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
充值订单查询工具
根据手机号和活动ID查询近三个月内的充值订单状态
"""

import requests
import json
import time
import hashlib
import hmac
import random
import string
from datetime import datetime
from typing import Dict


class ChargeOrderQuery:
    """充值订单查询类"""

    def __init__(self):
        """
        初始化查询工具 - 生产环境
        """
        self.base_url = "https://dev.coc.10086.cn/coc"
        self.api_path = "/gateway/api/common/buy/charge/order"
        self.full_url = f"{self.base_url}{self.api_path}"

        # 认证信息
        self.app_id = "37714611863543849465"
        self.app_key = "0a55bdc0379d449cafe68e29387af6d0"

        # 固定活动ID
        self.activity_id = 1958057766622400512
        
        # 订单状态映射
        self.status_map = {
            100: "初始态",
            200: "待支付",
            201: "支付中",
            202: "支付成功",
            203: "支付失败",
            300: "待订购",
            301: "订购中",
            302: "订购成功",
            303: "订购失败",
            304: "订购待重试"
        }



    def generate_nonce(self, length: int = 4) -> str:
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

    def generate_signature(self, method: str, uri: str, body: str, timestamp: str, nonce: str) -> str:
        """
        生成HMAC签名

        Args:
            method: HTTP方法
            uri: 请求路径
            body: 请求体
            timestamp: 时间戳
            nonce: 随机数

        Returns:
            签名字符串
        """
        # 构建签名字符串
        sign_string = f"{method}\n{uri}\n{body}\n{timestamp}\n{nonce}"

        # 生成HMAC-SHA256签名
        signature = hmac.new(
            self.app_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest().upper()

        return signature

    def get_auth_headers(self, request_body: str) -> Dict[str, str]:
        """
        生成认证请求头

        Args:
            request_body: 请求体JSON字符串

        Returns:
            认证请求头字典
        """
        # 生成时间戳和随机数
        timestamp = str(int(time.time() * 1000))
        nonce = self.generate_nonce()

        # 生成签名
        signature = self.generate_signature("POST", self.api_path, request_body, timestamp, nonce)

        # 构建认证请求头
        auth_headers = {
            "X-Hmac-Auth-Secret-Id": self.app_id,
            "X-Hmac-Auth-Timestamp": timestamp,
            "X-Hmac-Auth-Nonce": nonce,
            "X-Hmac-Auth-Signature": signature
        }

        return auth_headers

    def query_orders(self, mobile: str) -> Dict:
        """
        查询充值订单

        Args:
            mobile: 手机号

        Returns:
            查询结果字典
        """
        # 构建请求数据
        request_data = {
            "mobile": mobile,
            "activityId": self.activity_id
        }
        
        # 构建请求体JSON字符串
        request_body = json.dumps(request_data, separators=(',', ':'), ensure_ascii=False)

        # 设置基础请求头
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "ChargeOrderQuery/1.0"
        }

        # 添加认证请求头
        auth_headers = self.get_auth_headers(request_body)
        headers.update(auth_headers)
        
        try:
            print(f"正在查询手机号 {mobile} 的充值订单...")
            print(f"活动ID: {self.activity_id}")
            print(f"请求URL: {self.full_url}")
            print(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
            print("-" * 50)
            
            # 发送POST请求
            response = requests.post(
                self.full_url,
                data=request_body,
                headers=headers,
                timeout=30
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                "code": "-1",
                "message": f"请求异常: {str(e)}",
                "data": None
            }
        except json.JSONDecodeError as e:
            return {
                "code": "-2", 
                "message": f"响应解析异常: {str(e)}",
                "data": None
            }
    
    def format_timestamp(self, timestamp: int) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 毫秒级时间戳
            
        Returns:
            格式化后的时间字符串
        """
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return f"时间戳格式错误: {timestamp}"
    
    def get_status_desc(self, status_code: int) -> str:
        """
        获取状态描述
        
        Args:
            status_code: 状态码
            
        Returns:
            状态描述
        """
        return self.status_map.get(status_code, f"未知状态({status_code})")
    
    def print_results(self, result: Dict):
        """
        打印查询结果
        
        Args:
            result: 查询结果
        """
        print("查询结果:")
        print(f"响应码: {result.get('code', 'N/A')}")
        print(f"响应消息: {result.get('message', 'N/A')}")
        
        data = result.get('data')
        if data is None:
            print("未查询到订单数据")
            return
            
        if not data:
            print("订单列表为空")
            return
            
        print(f"\n共查询到 {len(data)} 笔订单:")
        print("-" * 80)
        print(f"{'序号':<4} {'手机号':<12} {'订单时间':<20} {'状态码':<6} {'状态描述':<10}")
        print("-" * 80)
        
        for i, order in enumerate(data, 1):
            mobile = order.get('mobile', 'N/A')
            order_time = self.format_timestamp(order.get('orderTime', 0))
            status_code = order.get('orderStatus', 0)
            status_desc = self.get_status_desc(status_code)
            
            print(f"{i:<4} {mobile:<12} {order_time:<20} {status_code:<6} {status_desc:<10}")


def main():
    """主函数"""
    print("=" * 60)
    print("充值订单查询工具 - 生产环境")
    print("=" * 60)

    # 创建查询实例
    query_tool = ChargeOrderQuery()
    print(f"\n查询地址: {query_tool.full_url}")
    print("✓ 准备就绪")
    
    # 循环查询
    while True:
        print("\n" + "=" * 60)
        
        # 输入手机号
        while True:
            mobile = input("请输入手机号: ").strip()
            if mobile:
                break
            print("手机号不能为空，请重新输入!")

        print(f"使用固定活动ID: {query_tool.activity_id}")

        # 执行查询
        result = query_tool.query_orders(mobile)
        
        # 打印结果
        query_tool.print_results(result)
        
        # 询问是否继续
        print("\n" + "-" * 60)
        continue_choice = input("是否继续查询? (y/n, 默认为y): ").strip().lower()
        if continue_choice in ['n', 'no', '否']:
            break
    
    print("\n查询结束，感谢使用!")


if __name__ == "__main__":
    main()
