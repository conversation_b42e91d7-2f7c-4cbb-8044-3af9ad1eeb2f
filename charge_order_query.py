#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
充值订单查询工具
根据手机号和活动ID查询近三个月内的充值订单状态
"""

import requests
import json
from datetime import datetime
from typing import Dict


class ChargeOrderQuery:
    """充值订单查询类"""

    def __init__(self):
        """
        初始化查询工具 - 生产环境
        """
        self.base_url = "https://dev.coc.10086.cn/coc"
        self.api_path = "/gateway/api/common/buy/charge/order"
        self.full_url = f"{self.base_url}{self.api_path}"
        
        # 订单状态映射
        self.status_map = {
            100: "初始态",
            200: "待支付",
            201: "支付中",
            202: "支付成功",
            203: "支付失败",
            300: "待订购",
            301: "订购中",
            302: "订购成功",
            303: "订购失败",
            304: "订购待重试"
        }


    
    def query_orders(self, mobile: str, activity_id: int) -> Dict:
        """
        查询充值订单
        
        Args:
            mobile: 手机号
            activity_id: 活动ID
            
        Returns:
            查询结果字典
        """
        # 构建请求数据
        request_data = {
            "mobile": mobile,
            "activityId": activity_id
        }
        
        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "ChargeOrderQuery/1.0"
        }
        
        try:
            print(f"正在查询手机号 {mobile} 的充值订单...")
            print(f"活动ID: {activity_id}")
            print(f"请求URL: {self.full_url}")
            print(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
            print("-" * 50)
            
            # 发送POST请求
            response = requests.post(
                self.full_url,
                json=request_data,
                headers=headers,
                timeout=30
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                "code": "-1",
                "message": f"请求异常: {str(e)}",
                "data": None
            }
        except json.JSONDecodeError as e:
            return {
                "code": "-2", 
                "message": f"响应解析异常: {str(e)}",
                "data": None
            }
    
    def format_timestamp(self, timestamp: int) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 毫秒级时间戳
            
        Returns:
            格式化后的时间字符串
        """
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return f"时间戳格式错误: {timestamp}"
    
    def get_status_desc(self, status_code: int) -> str:
        """
        获取状态描述
        
        Args:
            status_code: 状态码
            
        Returns:
            状态描述
        """
        return self.status_map.get(status_code, f"未知状态({status_code})")
    
    def print_results(self, result: Dict):
        """
        打印查询结果
        
        Args:
            result: 查询结果
        """
        print("查询结果:")
        print(f"响应码: {result.get('code', 'N/A')}")
        print(f"响应消息: {result.get('message', 'N/A')}")
        
        data = result.get('data')
        if data is None:
            print("未查询到订单数据")
            return
            
        if not data:
            print("订单列表为空")
            return
            
        print(f"\n共查询到 {len(data)} 笔订单:")
        print("-" * 80)
        print(f"{'序号':<4} {'手机号':<12} {'订单时间':<20} {'状态码':<6} {'状态描述':<10}")
        print("-" * 80)
        
        for i, order in enumerate(data, 1):
            mobile = order.get('mobile', 'N/A')
            order_time = self.format_timestamp(order.get('orderTime', 0))
            status_code = order.get('orderStatus', 0)
            status_desc = self.get_status_desc(status_code)
            
            print(f"{i:<4} {mobile:<12} {order_time:<20} {status_code:<6} {status_desc:<10}")


def main():
    """主函数"""
    print("=" * 60)
    print("充值订单查询工具 - 生产环境")
    print("=" * 60)

    # 创建查询实例
    query_tool = ChargeOrderQuery()
    print(f"\n查询地址: {query_tool.full_url}")
    print("✓ 准备就绪")
    
    # 循环查询
    while True:
        print("\n" + "=" * 60)
        
        # 输入手机号
        while True:
            mobile = input("请输入手机号: ").strip()
            if mobile:
                break
            print("手机号不能为空，请重新输入!")
        
        # 输入活动ID
        while True:
            try:
                activity_id_str = input("请输入活动ID: ").strip()
                activity_id = int(activity_id_str)
                break
            except ValueError:
                print("活动ID必须是数字，请重新输入!")
        
        # 执行查询
        result = query_tool.query_orders(mobile, activity_id)
        
        # 打印结果
        query_tool.print_results(result)
        
        # 询问是否继续
        print("\n" + "-" * 60)
        continue_choice = input("是否继续查询? (y/n, 默认为y): ").strip().lower()
        if continue_choice in ['n', 'no', '否']:
            break
    
    print("\n查询结束，感谢使用!")


if __name__ == "__main__":
    main()
